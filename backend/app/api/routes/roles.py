"""
角色API路由

处理与角色相关的所有API请求
"""
from flask import Blueprint, request, jsonify
from app.models import db
from app.services.role_service import RoleService

# 创建Blueprint
role_bp = Blueprint('role_api', __name__)

# 创建服务实例
role_service = RoleService()

@role_bp.route('/roles', methods=['GET'])
def get_roles():
    """获取所有角色列表"""
    roles = role_service.get_all_roles()
    # 格式化角色对象为API响应格式
    formatted_roles = [role_service.format_role_for_api(role) for role in roles]
    return jsonify({'roles': formatted_roles})

@role_bp.route('/roles/with-details', methods=['GET'])
def get_roles_with_details():
    """获取所有角色及其关联的能力和知识库信息"""
    try:
        roles_with_details = role_service.get_all_roles_with_details()
        return jsonify(roles_with_details)
    except Exception as e:
        return jsonify({'error': f'获取角色详细信息失败: {str(e)}'}), 500

@role_bp.route('/roles/<int:role_id>', methods=['GET'])
def get_role(role_id):
    """获取特定角色详情"""
    role = role_service.get_role_by_id(role_id)
    if role:
        # 格式化角色对象为API响应格式
        formatted_role = role_service.format_role_for_api(role)
        return jsonify(formatted_role)
    return jsonify({'error': 'Role not found'}), 404

@role_bp.route('/roles', methods=['POST'])
def create_role():
    """创建新角色"""
    data = request.get_json()
    role = role_service.create_role(data)
    # 格式化角色对象为API响应格式
    formatted_role = role_service.format_role_for_api(role)
    return jsonify(formatted_role), 201

@role_bp.route('/roles/<int:role_id>', methods=['PUT'])
def update_role(role_id):
    """更新角色信息"""
    data = request.get_json()
    role = role_service.update_role(role_id, data)
    if role:
        # 格式化角色对象为API响应格式
        formatted_role = role_service.format_role_for_api(role)
        return jsonify(formatted_role)
    return jsonify({'error': 'Role not found'}), 404

@role_bp.route('/roles/<int:role_id>', methods=['DELETE'])
def delete_role(role_id):
    """删除角色"""
    success = role_service.delete_role(role_id)
    if success:
        return jsonify({'success': True})
    return jsonify({'error': 'Role not found'}), 404

@role_bp.route('/roles/model-configs', methods=['GET'])
def get_role_model_configs():
    """获取角色可用的模型配置"""
    configs = role_service.get_role_model_configs()
    return jsonify({'model_configs': configs})

@role_bp.route('/roles/predefined', methods=['GET'])
def get_predefined_roles():
    """获取预定义角色列表"""
    roles = role_service.get_predefined_roles()
    return jsonify({'roles': roles})

@role_bp.route('/roles/recent', methods=['GET'])
def get_recent_roles():
    """获取最近使用的角色列表"""
    # 获取最近使用的角色数量参数，默认5个
    limit = request.args.get('limit', 5, type=int)
    roles = role_service.get_recent_roles(limit)
    return jsonify({'roles': roles})

@role_bp.route('/roles/most-used', methods=['GET'])
def get_most_used_roles():
    """获取最常用的角色列表"""
    # 获取请求的角色数量参数，默认5个
    limit = request.args.get('limit', 5, type=int)
    roles = role_service.get_most_used_roles(limit)
    return jsonify({'roles': roles})

@role_bp.route('/agents/from-role/<int:role_id>', methods=['POST'])
def create_agent_from_role(role_id):
    """从角色创建智能体"""
    data = request.get_json() or {}
    agent = role_service.create_agent_from_role(role_id, data)
    if agent:
        return jsonify(agent), 201
    return jsonify({'error': 'Role not found'}), 404

@role_bp.route('/roles/<int:role_id>/test', methods=['POST'])
def test_role(role_id):
    """测试角色响应"""
    data = request.get_json()
    prompt = data.get('prompt', '你好，请介绍一下你自己。')
    system_prompt = data.get('system_prompt')

    # 提取高级参数
    advanced_params = {}
    for param in ['temperature', 'top_p', 'frequency_penalty',
                  'presence_penalty', 'max_tokens', 'stop_sequences']:
        if param in data:
            advanced_params[param] = data[param]

    # 记录请求数据，方便调试
    print(f"角色测试请求: role_id={role_id}, prompt={prompt[:30]}..., 高级参数={advanced_params}")

    # 从角色服务获取测试结果
    result = role_service.test_role(role_id, prompt, system_prompt, **advanced_params)
    return jsonify(result)

@role_bp.route('/roles/test-external-connection', methods=['POST'])
def test_external_connection():
    """测试外部角色连接"""
    data = request.get_json()

    # 验证必要参数
    platform = data.get('platform')
    if not platform:
        return jsonify({'error': '缺少平台类型参数'}), 400

    try:
        # 根据平台类型进行实际连接测试
        if platform == 'dify':
            response_mode = data.get('responseMode', 'blocking')
            if response_mode == 'streaming':
                # 流式响应，返回SSE流
                return test_dify_connection_stream(data)
            else:
                # 阻塞响应，返回JSON
                result = test_dify_connection(data)
                return jsonify(result)

        elif platform == 'openai':
            api_key = data.get('apiKey')
            assistant_id = data.get('assistantId')
            if not api_key or not assistant_id:
                return jsonify({'error': '缺少必要的OpenAI配置参数'}), 400
            # TODO: 实现OpenAI连接测试
            return jsonify({
                'success': True,
                'message': 'OpenAI平台连接测试成功（模拟）',
                'platform': platform
            })

        elif platform == 'coze':
            api_key = data.get('apiKey')
            bot_id = data.get('botId')
            if not api_key or not bot_id:
                return jsonify({'error': '缺少必要的Coze配置参数'}), 400
            # TODO: 实现Coze连接测试
            return jsonify({
                'success': True,
                'message': 'Coze平台连接测试成功（模拟）',
                'platform': platform
            })

        elif platform == 'custom':
            api_key = data.get('apiKey')
            api_server = data.get('apiServer')
            platform_name = data.get('platformName')
            if not api_key or not api_server or not platform_name:
                return jsonify({'error': '缺少必要的自定义平台配置参数'}), 400
            # TODO: 实现自定义平台连接测试
            return jsonify({
                'success': True,
                'message': f'{platform_name}平台连接测试成功（模拟）',
                'platform': platform
            })

        return jsonify({'error': f'不支持的平台类型: {platform}'}), 400

    except Exception as e:
        return jsonify({'error': f'连接测试失败: {str(e)}'}), 500


def test_dify_connection(data):
    """测试Dify平台连接"""
    import requests

    api_key = data.get('apiKey')
    api_server = data.get('apiServer')
    timeout = data.get('timeout', 60)  # 获取超时设置，默认60秒
    response_mode = data.get('responseMode', 'blocking')  # 获取响应模式设置

    if not api_key or not api_server:
        return {'error': '缺少必要的Dify配置参数'}

    # 直接使用用户提供的API服务器地址
    # 只检查是否以http或https开头
    if not api_server.startswith(('http://', 'https://')):
        return {'error': '请输入完整的URL地址，必须以http://或https://开头'}

    # 移除末尾的斜杠
    api_server = api_server.rstrip('/')

    try:
        # 简化测试逻辑，统一使用 chat-messages 端点进行连接测试
        test_query = "你好！请简单介绍一下你自己，这是一个连接测试。"

        # 使用最通用的 chat-messages 端点
        endpoint = f"{api_server}/chat-messages"
        test_payload = {
            "inputs": {},
            "query": test_query,
            "response_mode": response_mode,  # 使用角色设置中的响应模式
            "conversation_id": "",
            "user": "connection_test"
        }

        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

        print(f"原始API服务器: {data.get('apiServer')}")
        print(f"处理后的API服务器: {api_server}")
        print(f"测试Dify连接: {endpoint}")
        print(f"超时设置: {timeout}秒")
        print(f"响应模式: {response_mode}")
        print(f"请求头: {headers}")
        print(f"请求体: {test_payload}")

        # 先测试基础连接
        try:
            base_response = requests.get(api_server, timeout=min(timeout, 30))  # 基础连接测试最多30秒
            print(f"基础连接测试 - 状态码: {base_response.status_code}")
        except Exception as e:
            print(f"基础连接测试失败: {e}")

        # 发送测试请求
        response = requests.post(
            endpoint,
            json=test_payload,
            headers=headers,
            timeout=timeout  # 使用角色设置中的超时时间
        )

        print(f"响应状态码: {response.status_code}")

        if response.status_code == 200:
            # 根据响应模式处理不同的响应格式
            if response_mode == 'streaming':
                # 流式响应处理
                test_response = _parse_dify_streaming_response(response)
            else:
                # 阻塞响应处理
                print(f"响应内容: {response.text}")
                response_data = response.json()
                print(f"完整响应数据: {response_data}")
                test_response = _parse_dify_blocking_response(response_data)

            return {
                'success': True,
                'message': 'Dify应用连接测试成功',
                'platform': 'dify',
                'test_input': test_query,
                'test_output': test_response[:500] + ('...' if len(test_response) > 500 else '')
            }
        else:
            error_msg = f"API请求失败 (状态码: {response.status_code})"
            try:
                error_data = response.json()
                if 'message' in error_data:
                    error_msg += f": {error_data['message']}"
                elif 'error' in error_data:
                    error_msg += f": {error_data['error']}"
            except:
                error_msg += f": {response.text}"

            return {'error': error_msg}

    except requests.exceptions.Timeout:
        return {'error': 'Dify API请求超时，请检查网络连接和服务器地址'}
    except requests.exceptions.ConnectionError:
        return {'error': 'Dify API连接失败，请检查服务器地址是否正确'}
    except Exception as e:
        return {'error': f'Dify连接测试失败: {str(e)}'}


def _parse_dify_streaming_response(response):
    """解析Dify流式响应"""
    import json

    try:
        collected_content = []

        # 逐行读取流式响应
        for line in response.iter_lines(decode_unicode=True):
            if not line:
                continue

            print(f"收到流式数据行: {line}")

            # 使用Dify适配器的解析方法
            from app.services.conversation.adapters.dify_adapter import DifyAdapter

            # 创建临时适配器实例用于解析
            temp_adapter = DifyAdapter({})
            content, meta = temp_adapter.parse_streaming_chunk(line)

            if content:
                collected_content.append(content)
                print(f"解析到内容: {content}")

            if meta and meta.get('type') == 'done':
                print("流式响应结束")
                break

        if collected_content:
            result = ''.join(collected_content)
            print(f"流式响应完整内容: {result}")
            return result
        else:
            return "流式响应已接收，但未解析到内容"

    except Exception as e:
        print(f"解析流式响应失败: {e}")
        return f"流式响应解析失败: {str(e)}"


def _parse_dify_blocking_response(response_data):
    """解析Dify阻塞响应"""
    try:
        # 使用Dify适配器的解析方法
        from app.services.conversation.adapters.dify_adapter import DifyAdapter

        # 创建临时适配器实例用于解析
        temp_adapter = DifyAdapter({})
        result = temp_adapter.parse_response(response_data)

        print(f"阻塞响应解析结果: {result}")
        return result

    except Exception as e:
        print(f"解析阻塞响应失败: {e}")
        return f"阻塞响应解析失败: {str(e)}"


def test_dify_connection_stream(data):
    """测试Dify平台连接（流式响应）"""
    import requests
    from flask import Response

    api_key = data.get('apiKey')
    api_server = data.get('apiServer')
    timeout = data.get('timeout', 60)

    if not api_key or not api_server:
        # 返回错误的SSE格式
        def error_generator():
            import json
            yield f"data: {json.dumps({'error': '缺少必要的Dify配置参数'})}\n\n"
        return Response(error_generator(), mimetype='text/plain')

    # 直接使用用户提供的API服务器地址
    if not api_server.startswith(('http://', 'https://')):
        def error_generator():
            import json
            yield f"data: {json.dumps({'error': '请输入完整的URL地址，必须以http://或https://开头'})}\n\n"
        return Response(error_generator(), mimetype='text/plain')

    # 移除末尾的斜杠
    api_server = api_server.rstrip('/')

    def stream_generator():
        import json
        try:
            # 构建测试请求
            test_query = "你好！请简单介绍一下你自己，这是一个连接测试。"
            endpoint = f"{api_server}/chat-messages"
            test_payload = {
                "inputs": {},
                "query": test_query,
                "response_mode": "streaming",
                "conversation_id": "",
                "user": "connection_test"
            }

            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            print(f"流式测试Dify连接: {endpoint}")
            print(f"请求体: {test_payload}")

            # 发送流式请求
            response = requests.post(
                endpoint,
                json=test_payload,
                headers=headers,
                stream=True,
                timeout=timeout
            )

            print(f"流式响应状态码: {response.status_code}")

            if response.status_code == 200:
                # 发送成功状态
                yield f"data: {json.dumps({'status': 'connected', 'message': 'Dify连接成功，开始接收响应...'})}\n\n"

                # 使用Dify适配器解析流式响应
                from app.services.conversation.adapters.dify_adapter import DifyAdapter
                temp_adapter = DifyAdapter({})

                # 逐行处理流式响应
                for line in response.iter_lines(decode_unicode=True):
                    if not line:
                        continue

                    print(f"收到流式数据行: {line}")

                    # 解析响应块
                    content, meta = temp_adapter.parse_streaming_chunk(line)

                    if content:
                        # 发送内容块
                        yield f"data: {json.dumps({'type': 'content', 'content': content})}\n\n"
                        print(f"发送内容: {content}")

                    if meta and meta.get('type') == 'done':
                        # 发送完成信号
                        yield f"data: {json.dumps({'type': 'done', 'message': '流式响应完成'})}\n\n"
                        print("流式响应结束")
                        break

            else:
                # 发送错误信息
                try:
                    error_data = response.json()
                    error_msg = error_data.get('message', f'API请求失败 (状态码: {response.status_code})')
                except:
                    error_msg = f'API请求失败 (状态码: {response.status_code}): {response.text}'

                yield f"data: {json.dumps({'error': error_msg})}\n\n"

        except requests.exceptions.Timeout:
            yield f"data: {json.dumps({'error': 'Dify API请求超时，请检查网络连接和服务器地址'})}\n\n"
        except requests.exceptions.ConnectionError:
            yield f"data: {json.dumps({'error': 'Dify API连接失败，请检查服务器地址是否正确'})}\n\n"
        except Exception as e:
            yield f"data: {json.dumps({'error': f'Dify连接测试失败: {str(e)}'})}\n\n"

    return Response(stream_generator(), mimetype='text/plain')