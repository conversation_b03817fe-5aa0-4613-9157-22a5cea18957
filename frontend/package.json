{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-json": "^6.0.2", "@codemirror/lang-python": "^6.2.1", "@codemirror/theme-one-dark": "^6.1.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@lightenna/react-mermaid-diagram": "^1.0.21", "@monaco-editor/react": "^4.7.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@uiw/react-codemirror": "^4.24.2", "antd": "^5.26.7", "axios": "^1.11.0", "dayjs": "^1.11.13", "dotenv": "^16.6.1", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "eslint": "8.57.1", "http-proxy-middleware": "^3.0.5", "ini": "^5.0.0", "isomorphic-fetch": "^3.0.0", "katex": "^0.16.22", "lodash": "^4.17.21", "mermaid": "^11.9.0", "moment": "^2.30.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-json-view": "^1.21.3", "react-markdown": "^10.1.0", "react-resizable": "^3.0.5", "react-router-dom": "^7.7.1", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.6.1", "react-transition-group": "^4.4.5", "react-virtualized": "^9.22.6", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "socket.io-client": "^4.8.1", "uuid": "^11.1.0", "web-vitals": "^4.2.4"}, "scripts": {"start": "craco start", "dev": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "pnpm": {"ignoredBuiltDependencies": ["core-js", "core-js-pure"]}, "jest": {"moduleNameMapper": {"\\.(css|less|scss|sass)$": "identity-obj-proxy"}}, "devDependencies": {"@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@craco/craco": "^7.1.0", "identity-obj-proxy": "^3.0.0", "jest-environment-jsdom": "^29.7.0"}}